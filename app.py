from flask import Flask, request, jsonify
import psycopg2
import json
from datetime import datetime

app = Flask(__name__)

# PostgreSQL config
DB_CONFIG = {
    "dbname": "toolbox_db",
    "user": "toolbox_user",
    "password": "my-password",
    "host": "127.0.0.1",
    "port": "5432"
}

def get_connection():
    return psycopg2.connect(**DB_CONFIG)

# Initialize FAQ table if not exists
def init_faq_table():
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            CREATE TABLE IF NOT EXISTS faqs (
                id SERIAL PRIMARY KEY,
                question TEXT NOT NULL,
                answer TEXT NOT NULL,
                category VARCHAR(100) DEFAULT 'general',
                keywords TEXT[],
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW(),
                usage_count INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE
            );
            
            CREATE INDEX IF NOT EXISTS idx_faq_keywords ON faqs USING GIN(keywords);
            CREATE INDEX IF NOT EXISTS idx_faq_category ON faqs(category);
            CREATE INDEX IF NOT EXISTS idx_faq_question ON faqs USING GIN(to_tsvector('english', question));
        """)
        conn.commit()
        print("FAQ table initialized successfully")
    except Exception as e:
        print(f"Error initializing FAQ table: {e}")
    finally:
        cur.close()
        conn.close()

# FAQ TOOLS - Following your existing Flask route pattern

@app.route("/search-faq", methods=["POST"])
def search_faq():
    """Search FAQ entries by question text or keywords"""
    data = request.get_json()
    search_text = data.get("search_text", "")
    category = data.get("category", "")
    limit = data.get("limit", 10)
    
    try:
        conn = get_connection()
        cur = conn.cursor()
        
        if category:
            query = """
                SELECT id, question, answer, category, usage_count,
                       ts_rank(to_tsvector('english', question || ' ' || answer), 
                               plainto_tsquery('english', %s)) as rank
                FROM faqs 
                WHERE is_active = TRUE 
                AND category = %s
                AND (to_tsvector('english', question || ' ' || answer) @@ plainto_tsquery('english', %s)
                     OR question ILIKE %s
                     OR %s = ANY(keywords))
                ORDER BY rank DESC, usage_count DESC
                LIMIT %s
            """
            cur.execute(query, (search_text, category, search_text, f"%{search_text}%", search_text, limit))
        else:
            query = """
                SELECT id, question, answer, category, usage_count,
                       ts_rank(to_tsvector('english', question || ' ' || answer), 
                               plainto_tsquery('english', %s)) as rank
                FROM faqs 
                WHERE is_active = TRUE 
                AND (to_tsvector('english', question || ' ' || answer) @@ plainto_tsquery('english', %s)
                     OR question ILIKE %s
                     OR %s = ANY(keywords))
                ORDER BY rank DESC, usage_count DESC
                LIMIT %s
            """
            cur.execute(query, (search_text, search_text, f"%{search_text}%", search_text, limit))
        
        rows = cur.fetchall()
        columns = [desc[0] for desc in cur.description]
        result = [dict(zip(columns, row)) for row in rows]
        
        # Update usage count for found FAQs
        if result:
            faq_ids = [faq['id'] for faq in result]
            cur.execute("UPDATE faqs SET usage_count = usage_count + 1 WHERE id = ANY(%s)", (faq_ids,))
            conn.commit()
        
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/add-faq", methods=["POST"])
def add_faq():
    """Add new FAQ entry"""
    data = request.get_json()
    question = data.get("question", "")
    answer = data.get("answer", "")
    category = data.get("category", "general")
    keywords = data.get("keywords", [])
    
    if not question or not answer:
        return jsonify({"error": "Question and answer are required"})
    
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            INSERT INTO faqs (question, answer, category, keywords)
            VALUES (%s, %s, %s, %s)
            RETURNING id
        """, (question, answer, category, keywords))
        faq_id = cur.fetchone()[0]
        conn.commit()
        return jsonify({"status": "FAQ added successfully", "faq_id": faq_id})
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/update-faq", methods=["POST"])
def update_faq():
    """Update existing FAQ entry"""
    data = request.get_json()
    faq_id = data.get("faq_id")
    question = data.get("question")
    answer = data.get("answer")
    category = data.get("category")
    keywords = data.get("keywords")
    
    if not faq_id:
        return jsonify({"error": "faq_id is required"})
    
    try:
        conn = get_connection()
        cur = conn.cursor()
        
        # Build dynamic update query
        updates = []
        values = []
        
        if question:
            updates.append("question = %s")
            values.append(question)
        if answer:
            updates.append("answer = %s")
            values.append(answer)
        if category:
            updates.append("category = %s")
            values.append(category)
        if keywords is not None:  # Allow empty array
            updates.append("keywords = %s")
            values.append(keywords)
        
        if updates:
            updates.append("updated_at = NOW()")
            values.append(faq_id)
            
            query = f"UPDATE faqs SET {', '.join(updates)} WHERE id = %s"
            cur.execute(query, values)
            
            if cur.rowcount == 0:
                return jsonify({"error": "FAQ not found"})
            
            conn.commit()
            return jsonify({"status": "FAQ updated successfully"})
        else:
            return jsonify({"error": "No fields to update"})
            
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/popular-faqs", methods=["POST"])
def popular_faqs():
    """Get most frequently accessed FAQs"""
    data = request.get_json()
    limit = data.get("limit", 10)
    category = data.get("category", "")
    
    try:
        conn = get_connection()
        cur = conn.cursor()
        
        if category:
            query = """
                SELECT id, question, answer, category, usage_count, created_at
                FROM faqs 
                WHERE is_active = TRUE AND category = %s
                ORDER BY usage_count DESC, created_at DESC
                LIMIT %s
            """
            cur.execute(query, (category, limit))
        else:
            query = """
                SELECT id, question, answer, category, usage_count, created_at
                FROM faqs 
                WHERE is_active = TRUE
                ORDER BY usage_count DESC, created_at DESC
                LIMIT %s
            """
            cur.execute(query, (limit,))
        
        rows = cur.fetchall()
        columns = [desc[0] for desc in cur.description]
        result = [dict(zip(columns, row)) for row in rows]
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/list-faq-categories", methods=["POST"])
def list_faq_categories():
    """Get all FAQ categories with counts"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            SELECT category, COUNT(*) as count, MAX(updated_at) as last_updated
            FROM faqs 
            WHERE is_active = TRUE
            GROUP BY category
            ORDER BY count DESC, category
        """)
        rows = cur.fetchall()
        columns = [desc[0] for desc in cur.description]
        result = [dict(zip(columns, row)) for row in rows]
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/query-any-faq", methods=["POST"])
def query_any_faq():
    """Execute any SQL SELECT query on FAQ table"""
    data = request.get_json()
    query = data.get("sql", "")
    
    if not query:
        return jsonify({"error": "SQL query is required"})
    
    # Basic security check - only allow SELECT statements
    query_upper = query.strip().upper()
    if not query_upper.startswith('SELECT'):
        return jsonify({"error": "Only SELECT queries are allowed"})
    
    # Additional security - prevent dangerous operations
    dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
    if any(keyword in query_upper for keyword in dangerous_keywords):
        return jsonify({"error": "Query contains forbidden operations"})
    
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute(query)
        rows = cur.fetchall()
        columns = [desc[0] for desc in cur.description]
        result = [dict(zip(columns, row)) for row in rows]
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/delete-faq", methods=["POST"])
def delete_faq():
    """Soft delete FAQ entry (mark as inactive)"""
    data = request.get_json()
    faq_id = data.get("faq_id")
    
    if not faq_id:
        return jsonify({"error": "faq_id is required"})
    
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("UPDATE faqs SET is_active = FALSE, updated_at = NOW() WHERE id = %s", (faq_id,))
        
        if cur.rowcount == 0:
            return jsonify({"error": "FAQ not found"})
        
        conn.commit()
        return jsonify({"status": "FAQ deleted successfully"})
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

# YOUR EXISTING HOTEL/BOOKING TOOLS (unchanged)

@app.route("/booking-count-for-hotel", methods=["POST"])
def booking_count_for_hotel():
    data = request.get_json()
    hotel_name = data.get("hotel_name", "")
    try:
        conn = get_connection()
        cur = conn.cursor()
        query = """
            SELECT COUNT(*) AS total_bookings
            FROM bookings b
            JOIN rooms r ON b.room_id = r.room_id
            JOIN hotel h ON r.hotel_id = h.hotel_id
            WHERE h.name ILIKE %s
        """
        cur.execute(query, (f"%{hotel_name}%",))
        count = cur.fetchone()[0]
        return jsonify({"hotel": hotel_name, "total_bookings": count})
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/generate-all-tables-ddl", methods=["POST"])
def generate_all_tables_ddl():
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema='public' AND table_type='BASE TABLE'
        """)
        tables = cur.fetchall()
        ddl_statements = []
        for (table,) in tables:
            cur.execute(f"SELECT 'CREATE TABLE ' || '{table}' || E'\n(\n' || string_agg(column_name || ' ' || data_type, E',\n') || E'\n);' FROM information_schema.columns WHERE table_name='{table}'")
            ddl = cur.fetchone()[0]
            ddl_statements.append(ddl)
        return jsonify(ddl_statements)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/analyze-schema-and-query", methods=["POST"])
def analyze_schema_and_query():
    data = request.get_json()
    query = data.get("analysis_query", "")
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute(query)
        rows = cur.fetchall()
        columns = [desc[0] for desc in cur.description]
        result = [dict(zip(columns, row)) for row in rows]
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/list-tables", methods=["POST"])
def list_tables():
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        tables = [row[0] for row in cur.fetchall()]
        return jsonify(tables)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/execute-sql", methods=["POST"])
def execute_sql():
    data = request.get_json()
    query = data.get("query", "")
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute(query)
        if cur.description:  # SELECT query
            rows = cur.fetchall()
            columns = [desc[0] for desc in cur.description]
            result = [dict(zip(columns, row)) for row in rows]
            return jsonify(result)
        else:  # INSERT/UPDATE/DELETE
            conn.commit()
            return jsonify({"status": "Query executed successfully."})
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

# UTILITY ROUTES FOR FAQ MANAGEMENT

@app.route("/faq-stats", methods=["POST"])
def faq_stats():
    """Get overall FAQ statistics"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            SELECT 
                COUNT(*) as total_faqs,
                COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_faqs,
                COUNT(CASE WHEN is_active = FALSE THEN 1 END) as inactive_faqs,
                SUM(usage_count) as total_usage,
                AVG(usage_count) as avg_usage,
                COUNT(DISTINCT category) as total_categories
            FROM faqs
        """)
        row = cur.fetchone()
        columns = [desc[0] for desc in cur.description]
        result = dict(zip(columns, row))
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

@app.route("/get-faq-by-id", methods=["POST"])
def get_faq_by_id():
    """Get specific FAQ by ID"""
    data = request.get_json()
    faq_id = data.get("faq_id")
    
    if not faq_id:
        return jsonify({"error": "faq_id is required"})
    
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            SELECT id, question, answer, category, keywords, created_at, updated_at, usage_count, is_active
            FROM faqs 
            WHERE id = %s
        """, (faq_id,))
        
        row = cur.fetchone()
        if not row:
            return jsonify({"error": "FAQ not found"})
        
        columns = [desc[0] for desc in cur.description]
        result = dict(zip(columns, row))
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    init_faq_table()  # Initialize FAQ table on startup
    app.run(debug=True, host='0.0.0.0', port=5000)