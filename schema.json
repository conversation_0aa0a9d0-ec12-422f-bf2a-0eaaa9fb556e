{"openapi": "3.1.0", "info": {"title": "MCP Toolbox API", "version": "1.0.0", "description": "Toolset to interact with hotel database, PostgreSQL tools, and FAQ system via MCP."}, "servers": [{"url": " https://d939401a3052.ngrok-free.app"}], "paths": {"/generate-all-tables-ddl": {"post": {"summary": "Generate DDL for all tables", "operationId": "generateAllTablesDDL", "responses": {"200": {"description": "Generated DDL statements", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/analyze-schema-and-query": {"post": {"summary": "Analyze schema and run query", "operationId": "analyzeSchemaAndQuery", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"analysis_query": {"type": "string", "description": "SQL query to run based on schema understanding"}}, "required": ["analysis_query"]}}}}, "responses": {"200": {"description": "Query result", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}}}}}}}, "/list-tables": {"post": {"summary": "List all tables", "operationId": "listTables", "responses": {"200": {"description": "List of table names", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/execute-sql": {"post": {"summary": "Execute raw SQL query", "operationId": "executeSQL", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "SQL query to execute"}}, "required": ["query"]}}}}, "responses": {"200": {"description": "Execution result", "content": {"application/json": {"schema": {"oneOf": [{"type": "array", "items": {"type": "object"}}, {"type": "object", "properties": {"status": {"type": "string"}}}]}}}}}}}, "/search-faq": {"post": {"summary": "Search FAQ entries", "operationId": "searchFAQ", "description": "Search FAQ entries by question text or keywords with full-text search and ranking", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"search_text": {"type": "string", "description": "Text to search in questions and answers", "example": "how to connect database"}, "category": {"type": "string", "description": "Optional category filter", "example": "database"}, "limit": {"type": "integer", "description": "Maximum number of results (default 10)", "default": 10, "minimum": 1, "maximum": 100}}, "required": ["search_text"]}}}}, "responses": {"200": {"description": "Ranked FAQ search results", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "question": {"type": "string"}, "answer": {"type": "string"}, "category": {"type": "string"}, "usage_count": {"type": "integer"}, "rank": {"type": "number"}}}}}}}}}}, "/add-faq": {"post": {"summary": "Add new FAQ entry", "operationId": "addFAQ", "description": "Add a new FAQ entry to the database", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"question": {"type": "string", "description": "The FAQ question", "example": "How to connect to database?"}, "answer": {"type": "string", "description": "The FAQ answer", "example": "Use the connection string with proper credentials..."}, "category": {"type": "string", "description": "Category for the FAQ (default: general)", "default": "general", "example": "database"}, "keywords": {"type": "array", "items": {"type": "string"}, "description": "Array of keywords for better searchability", "example": ["connection", "database", "setup"]}}, "required": ["question", "answer"]}}}}, "responses": {"200": {"description": "FAQ added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "faq_id": {"type": "integer"}}}}}}}}}, "/update-faq": {"post": {"summary": "Update existing FAQ entry", "operationId": "updateFAQ", "description": "Update an existing FAQ entry", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"faq_id": {"type": "integer", "description": "ID of the FAQ to update"}, "question": {"type": "string", "description": "Updated question"}, "answer": {"type": "string", "description": "Updated answer"}, "category": {"type": "string", "description": "Updated category"}, "keywords": {"type": "array", "items": {"type": "string"}, "description": "Updated keywords array"}}, "required": ["faq_id"]}}}}, "responses": {"200": {"description": "FAQ updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}}}}}}}}, "/popular-faqs": {"post": {"summary": "Get most frequently accessed FAQs", "operationId": "popularFAQs", "description": "Retrieve the most frequently accessed FAQ entries", "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"limit": {"type": "integer", "description": "Maximum number of results (default 10)", "default": 10, "minimum": 1, "maximum": 100}, "category": {"type": "string", "description": "Optional category filter"}}}}}}, "responses": {"200": {"description": "Most popular FAQ entries", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "question": {"type": "string"}, "answer": {"type": "string"}, "category": {"type": "string"}, "usage_count": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}}}, "/list-faq-categories": {"post": {"summary": "List all FAQ categories", "operationId": "listFAQCategories", "description": "Get all FAQ categories with counts and last updated information", "responses": {"200": {"description": "List of FAQ categories with statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"category": {"type": "string"}, "count": {"type": "integer"}, "last_updated": {"type": "string", "format": "date-time"}}}}}}}}}}, "/query-any-faq": {"post": {"summary": "Execute custom SQL query on FAQ table", "operationId": "queryAnyFAQ", "description": "Execute any SELECT SQL query on the FAQ table (read-only)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"sql": {"type": "string", "description": "Raw SQL SELECT query to run on FAQ table", "example": "SELECT * FROM faqs WHERE category = 'database' LIMIT 5"}}, "required": ["sql"]}}}}, "responses": {"200": {"description": "Query execution result", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}}}}}}}, "/delete-faq": {"post": {"summary": "Delete FAQ entry", "operationId": "deleteFAQ", "description": "Soft delete FAQ entry (mark as inactive)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"faq_id": {"type": "integer", "description": "ID of the FAQ to delete"}}, "required": ["faq_id"]}}}}, "responses": {"200": {"description": "FAQ deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}}}}}}}}, "/faq-stats": {"post": {"summary": "Get FAQ statistics", "operationId": "faqStats", "description": "Get overall FAQ system statistics and metrics", "responses": {"200": {"description": "FAQ system statistics", "content": {"application/json": {"schema": {"type": "object", "properties": {"total_faqs": {"type": "integer", "description": "Total number of FAQs"}, "active_faqs": {"type": "integer", "description": "Number of active FAQs"}, "inactive_faqs": {"type": "integer", "description": "Number of inactive FAQs"}, "total_usage": {"type": "integer", "description": "Total usage count across all FAQs"}, "avg_usage": {"type": "number", "description": "Average usage count per FAQ"}, "total_categories": {"type": "integer", "description": "Number of distinct categories"}}}}}}}}}, "/get-faq-by-id": {"post": {"summary": "Get FAQ by ID", "operationId": "getFAQById", "description": "Retrieve a specific FAQ entry by its ID", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"faq_id": {"type": "integer", "description": "ID of the FAQ to retrieve"}}, "required": ["faq_id"]}}}}, "responses": {"200": {"description": "FAQ details", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer"}, "question": {"type": "string"}, "answer": {"type": "string"}, "category": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "usage_count": {"type": "integer"}, "is_active": {"type": "boolean"}}}}}}}}}}, "components": {"schemas": {"Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}}}, "FAQ": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique FAQ identifier"}, "question": {"type": "string", "description": "The FAQ question"}, "answer": {"type": "string", "description": "The FAQ answer"}, "category": {"type": "string", "description": "FAQ category"}, "keywords": {"type": "array", "items": {"type": "string"}, "description": "Keywords for search optimization"}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "usage_count": {"type": "integer", "description": "Number of times this FAQ was accessed"}, "is_active": {"type": "boolean", "description": "Whether the FAQ is active"}}}, "FAQSearchResult": {"allOf": [{"$ref": "#/components/schemas/FAQ"}, {"type": "object", "properties": {"rank": {"type": "number", "description": "Search relevance ranking"}}}]}}}}