#!/usr/bin/env python3
"""
MCP YAML Generator from Database Schema
Automatically generates YAML configurations based on database schema with customizable prompts
"""

import json
import yaml
import sqlite3
import psycopg2
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

@dataclass
class ColumnInfo:
    name: str
    type: str
    nullable: bool
    primary_key: bool
    foreign_key: Optional[str] = None
    default_value: Optional[str] = None

@dataclass
class TableInfo:
    name: str
    columns: List[ColumnInfo]
    relationships: List[Dict[str, str]]

class DatabaseSchemaExtractor(ABC):
    """Abstract base class for database schema extraction"""
    
    @abstractmethod
    def extract_schema(self, connection_string: str) -> List[TableInfo]:
        pass



class PostgreSQLSchemaExtractor(DatabaseSchemaExtractor):
    """PostgreSQL schema extractor"""
    
    def extract_schema(self, connection_string: str) -> List[TableInfo]:
        tables = []
        conn = psycopg2.connect(connection_string)
        cursor = conn.cursor()
        
        # Get all tables in public schema
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
        """)
        table_names = [row[0] for row in cursor.fetchall()]
        
        for table_name in table_names:
            # Get column information
            cursor.execute("""
                SELECT 
                    column_name,
                    data_type,
                    is_nullable,
                    column_default
                FROM information_schema.columns
                WHERE table_name = %s AND table_schema = 'public'
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns_data = cursor.fetchall()
            columns = []
            
            for col_data in columns_data:
                column = ColumnInfo(
                    name=col_data[0],
                    type=col_data[1],
                    nullable=col_data[2] == 'YES',
                    primary_key=False,  # Will be updated below
                    default_value=col_data[3]
                )
                columns.append(column)
            
            # Get primary key information
            cursor.execute("""
                SELECT column_name
                FROM information_schema.key_column_usage
                WHERE table_name = %s AND constraint_name LIKE '%_pkey';
            """, (table_name,))
            
            pk_columns = [row[0] for row in cursor.fetchall()]
            for column in columns:
                if column.name in pk_columns:
                    column.primary_key = True
            
            # Get foreign key relationships
            cursor.execute("""
                SELECT 
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = %s;
            """, (table_name,))
            
            fk_data = cursor.fetchall()
            relationships = []
            
            for fk in fk_data:
                relationships.append({
                    "from_column": fk[0],
                    "to_table": fk[1],
                    "to_column": fk[2]
                })
            
            tables.append(TableInfo(
                name=table_name,
                columns=columns,
                relationships=relationships
            ))
        
        conn.close()
        return tables

class YAMLGenerator:
    """Generates YAML configurations based on database schema and prompts"""
    
    def __init__(self):
        self.formatting_prompts = {
            "api_config": {
                "description": "Generate REST API configuration",
                "template": self._generate_api_config
            },
            "orm_config": {
                "description": "Generate ORM model configuration",
                "template": self._generate_orm_config
            },
            "validation_config": {
                "description": "Generate data validation configuration",
                "template": self._generate_validation_config
            },
            "microservice_config": {
                "description": "Generate microservice configuration",
                "template": self._generate_microservice_config
            },
            "kubernetes_config": {
                "description": "Generate Kubernetes deployment configuration",
                "template": self._generate_kubernetes_config
            }
        }
    
    def generate_yaml(self, tables: List[TableInfo], format_type: str, **kwargs) -> str:
        """Generate YAML based on schema and format type"""
        if format_type not in self.formatting_prompts:
            raise ValueError(f"Unknown format type: {format_type}")
        
        template_func = self.formatting_prompts[format_type]["template"]
        config = template_func(tables, **kwargs)
        
        return yaml.dump(config, default_flow_style=False, sort_keys=False, indent=2)
    
    def _generate_api_config(self, tables: List[TableInfo], **kwargs) -> Dict[str, Any]:
        """Generate REST API configuration"""
        api_version = kwargs.get('api_version', 'v1')
        base_path = kwargs.get('base_path', '/api')
        
        config = {
            "openapi": "3.0.0",
            "info": {
                "title": "Generated API",
                "version": api_version,
                "description": "Auto-generated API from database schema"
            },
            "servers": [
                {"url": f"{base_path}/{api_version}"}
            ],
            "paths": {},
            "components": {
                "schemas": {}
            }
        }
        
        for table in tables:
            # Generate schema definitions
            schema_properties = {}
            required_fields = []
            
            for column in table.columns:
                prop_type = self._map_db_type_to_openapi(column.type)
                schema_properties[column.name] = {"type": prop_type}
                
                if not column.nullable and not column.primary_key:
                    required_fields.append(column.name)
            
            config["components"]["schemas"][table.name.title()] = {
                "type": "object",
                "properties": schema_properties,
                "required": required_fields
            }
            
            # Generate API paths
            resource_path = f"/{table.name.lower()}"
            config["paths"][resource_path] = {
                "get": {
                    "summary": f"Get all {table.name}",
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {"$ref": f"#/components/schemas/{table.name.title()}"}
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "summary": f"Create {table.name}",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "schema": {"$ref": f"#/components/schemas/{table.name.title()}"}
                            }
                        }
                    },
                    "responses": {
                        "201": {"description": "Created"}
                    }
                }
            }
            
            # Individual resource paths
            pk_column = next((col.name for col in table.columns if col.primary_key), "id")
            config["paths"][f"{resource_path}/{{{pk_column}}}"] = {
                "get": {
                    "summary": f"Get {table.name} by ID",
                    "parameters": [
                        {
                            "name": pk_column,
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {"$ref": f"#/components/schemas/{table.name.title()}"}
                                }
                            }
                        }
                    }
                }
            }
        
        return config
    
    def _generate_orm_config(self, tables: List[TableInfo], **kwargs) -> Dict[str, Any]:
        """Generate ORM model configuration"""
        orm_type = kwargs.get('orm_type', 'sqlalchemy')
        
        config = {
            "models": {},
            "database": {
                "type": kwargs.get('db_type', 'postgresql'),
                "connection": kwargs.get('connection_string', 'postgresql://user:pass@localhost/db')
            }
        }
        
        for table in tables:
            model_config = {
                "table_name": table.name,
                "fields": {},
                "relationships": []
            }
            
            for column in table.columns:
                field_config = {
                    "type": self._map_db_type_to_orm(column.type),
                    "nullable": column.nullable,
                    "primary_key": column.primary_key
                }
                
                if column.default_value:
                    field_config["default"] = column.default_value
                
                model_config["fields"][column.name] = field_config
            
            # Add relationships
            for rel in table.relationships:
                model_config["relationships"].append({
                    "type": "foreign_key",
                    "field": rel["from_column"],
                    "references": f"{rel['to_table']}.{rel['to_column']}"
                })
            
            config["models"][table.name.title()] = model_config
        
        return config
    
    def _generate_validation_config(self, tables: List[TableInfo], **kwargs) -> Dict[str, Any]:
        """Generate data validation configuration"""
        config = {
            "validation_rules": {},
            "custom_validators": kwargs.get('custom_validators', {})
        }
        
        for table in tables:
            table_rules = {
                "fields": {},
                "constraints": []
            }
            
            for column in table.columns:
                field_rules = []
                
                if not column.nullable:
                    field_rules.append("required")
                
                # Add type-specific validations
                if "varchar" in column.type.lower() or "text" in column.type.lower():
                    field_rules.append("string")
                    # Extract length if specified
                    if "(" in column.type:
                        max_length = column.type.split("(")[1].split(")")[0]
                        field_rules.append(f"max_length:{max_length}")
                elif "int" in column.type.lower():
                    field_rules.append("integer")
                elif "decimal" in column.type.lower() or "numeric" in column.type.lower():
                    field_rules.append("numeric")
                elif "date" in column.type.lower():
                    field_rules.append("date")
                elif "email" in column.name.lower():
                    field_rules.append("email")
                
                table_rules["fields"][column.name] = field_rules
            
            # Add unique constraints for primary keys
            pk_columns = [col.name for col in table.columns if col.primary_key]
            if pk_columns:
                table_rules["constraints"].append({
                    "type": "unique",
                    "fields": pk_columns
                })
            
            config["validation_rules"][table.name] = table_rules
        
        return config
    
    def _generate_microservice_config(self, tables: List[TableInfo], **kwargs) -> Dict[str, Any]:
        """Generate microservice configuration"""
        service_name = kwargs.get('service_name', 'data-service')
        
        config = {
            "service": {
                "name": service_name,
                "version": kwargs.get('version', '1.0.0'),
                "port": kwargs.get('port', 8080)
            },
            "database": {
                "type": kwargs.get('db_type', 'postgresql'),
                "host": kwargs.get('db_host', 'localhost'),
                "port": kwargs.get('db_port', 5432),
                "name": kwargs.get('db_name', 'mydb')
            },
            "entities": {},
            "endpoints": []
        }
        
        for table in tables:
            # Entity configuration
            config["entities"][table.name] = {
                "table": table.name,
                "fields": [col.name for col in table.columns],
                "primary_key": [col.name for col in table.columns if col.primary_key]
            }
            
            # Service endpoints
            config["endpoints"].extend([
                {
                    "path": f"/{table.name.lower()}",
                    "methods": ["GET", "POST"],
                    "entity": table.name
                },
                {
                    "path": f"/{table.name.lower()}/{{id}}",
                    "methods": ["GET", "PUT", "DELETE"],
                    "entity": table.name
                }
            ])
        
        return config
    
    def _generate_kubernetes_config(self, tables: List[TableInfo], **kwargs) -> Dict[str, Any]:
        """Generate Kubernetes deployment configuration"""
        app_name = kwargs.get('app_name', 'db-app')
        namespace = kwargs.get('namespace', 'default')
        
        config = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": f"{app_name}-deployment",
                "namespace": namespace,
                "labels": {
                    "app": app_name
                }
            },
            "spec": {
                "replicas": kwargs.get('replicas', 3),
                "selector": {
                    "matchLabels": {
                        "app": app_name
                    }
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "app": app_name
                        }
                    },
                    "spec": {
                        "containers": [
                            {
                                "name": app_name,
                                "image": kwargs.get('image', f"{app_name}:latest"),
                                "ports": [
                                    {
                                        "containerPort": kwargs.get('port', 8080)
                                    }
                                ],
                                "env": [
                                    {
                                        "name": "DB_HOST",
                                        "value": kwargs.get('db_host', 'postgres-service')
                                    },
                                    {
                                        "name": "DB_NAME",
                                        "value": kwargs.get('db_name', 'mydb')
                                    }
                                ],
                                "resources": {
                                    "requests": {
                                        "memory": "256Mi",
                                        "cpu": "250m"
                                    },
                                    "limits": {
                                        "memory": "512Mi",
                                        "cpu": "500m"
                                    }
                                }
                            }
                        ]
                    }
                }
            }
        }
        
        return config
    
    def _map_db_type_to_openapi(self, db_type: str) -> str:
        """Map database types to OpenAPI types"""
        type_mapping = {
            "varchar": "string",
            "text": "string",
            "char": "string",
            "integer": "integer",
            "int": "integer",
            "bigint": "integer",
            "decimal": "number",
            "numeric": "number",
            "float": "number",
            "real": "number",
            "boolean": "boolean",
            "bool": "boolean",
            "date": "string",
            "datetime": "string",
            "timestamp": "string",
            "time": "string"
        }
        
        for db_type_key, openapi_type in type_mapping.items():
            if db_type_key in db_type.lower():
                return openapi_type
        
        return "string"  # Default fallback
    
    def _map_db_type_to_orm(self, db_type: str) -> str:
        """Map database types to ORM types"""
        type_mapping = {
            "varchar": "String",
            "text": "Text",
            "char": "String",
            "integer": "Integer",
            "int": "Integer",
            "bigint": "BigInteger",
            "decimal": "Decimal",
            "numeric": "Numeric",
            "float": "Float",
            "real": "Float",
            "boolean": "Boolean",
            "bool": "Boolean",
            "date": "Date",
            "datetime": "DateTime",
            "timestamp": "DateTime",
            "time": "Time"
        }
        
        for db_type_key, orm_type in type_mapping.items():
            if db_type_key in db_type.lower():
                return orm_type
        
        return "String"  # Default fallback

class MCPYAMLGenerator:
    """Main class for MCP YAML generation"""
    
    def __init__(self):
        self.extractors = {
            "sqlite": SQLiteSchemaExtractor(),
            "postgresql": PostgreSQLSchemaExtractor()
        }
        self.generator = YAMLGenerator()
    
    def generate_from_schema(self, 
                           db_type: str, 
                           connection_string: str, 
                           format_type: str,
                           output_file: Optional[str] = None,
                           **kwargs) -> str:
        """Generate YAML from database schema"""
        
        if db_type not in self.extractors:
            raise ValueError(f"Unsupported database type: {db_type}")
        
        # Extract schema
        extractor = self.extractors[db_type]
        tables = extractor.extract_schema(connection_string)
        
        # Generate YAML
        yaml_content = self.generator.generate_yaml(tables, format_type, **kwargs)
        
        # Save to file if specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(yaml_content)
            print(f"YAML configuration saved to: {output_file}")
        
        return yaml_content
    
    def list_available_formats(self) -> Dict[str, str]:
        """List available YAML format types"""
        return {
            format_type: info["description"] 
            for format_type, info in self.generator.formatting_prompts.items()
        }

# Example usage and demo
def main():
    """Example usage of the MCP YAML Generator"""
    
    # Initialize the generator
    mcp_generator = MCPYAMLGenerator()
    
    # Example 1: Generate API configuration from SQLite database
    print("=== Example 1: API Configuration from SQLite ===")
    try:
        # For demo purposes, create a sample SQLite database
        create_sample_sqlite_db("sample.db")
        
        api_yaml = mcp_generator.generate_from_schema(
            db_type="sqlite",
            connection_string="sample.db",
            format_type="api_config",
            api_version="v1",
            base_path="/api"
        )
        print(api_yaml)
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Example 2: Generate ORM configuration
    print("=== Example 2: ORM Configuration ===")
    try:
        orm_yaml = mcp_generator.generate_from_schema(
            db_type="sqlite",
            connection_string="sample.db",
            format_type="orm_config",
            orm_type="sqlalchemy",
            db_type="sqlite"
        )
        print(orm_yaml)
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Example 3: Generate validation configuration
    print("=== Example 3: Validation Configuration ===")
    try:
        validation_yaml = mcp_generator.generate_from_schema(
            db_type="sqlite",
            connection_string="sample.db",
            format_type="validation_config"
        )
        print(validation_yaml)
        
    except Exception as e:
        print(f"Error: {e}")
    
    # List available formats
    print("\n=== Available Format Types ===")
    formats = mcp_generator.list_available_formats()
    for fmt, desc in formats.items():
        print(f"- {fmt}: {desc}")

def create_sample_sqlite_db(db_path: str):
    """Create a sample SQLite database for demonstration"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create sample tables
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
    """)
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            content TEXT,
            user_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """)
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT
        )
    """)
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS post_categories (
            post_id INTEGER,
            category_id INTEGER,
            PRIMARY KEY (post_id, category_id),
            FOREIGN KEY (post_id) REFERENCES posts (id),
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
    """)
    
    conn.commit()
    conn.close()

if __name__ == "__main__":
    main()