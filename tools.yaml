sources:
  my-pg-source:
    kind: postgres
    host: 127.0.0.1
    port: 5432
    database: toolbox_db
    user: toolbox_user
    password: my-password

tools:
  search-hotels-by-name:
    kind: postgres-sql
    source: my-pg-source
    description: Search for hotels based on name.
    parameters:
      - name: name
        type: string
        description: The name of the hotel.
    statement: SELECT * FROM hotels WHERE hotel_name ILIKE '%' || $1 || '%';

  list-tables:
    kind: postgres-sql
    source: my-pg-source
    description: List all tables in the connected database.
    statement: |
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;

  
    

  generate-all-tables-ddl:
    kind: postgres-sql
    source: my-pg-source
    description: Generate DDL (CREATE TABLE statements) for all tables in the database.
    statement: |
      WITH table_definitions AS (
        SELECT 
          t.table_name,
          string_agg(
            '  ' || c.column_name || ' ' || 
            CASE 
              WHEN c.data_type = 'character varying' THEN 'VARCHAR(' || c.character_maximum_length || ')'
              WHEN c.data_type = 'character' THEN 'CHAR(' || c.character_maximum_length || ')'
              WHEN c.data_type = 'numeric' THEN 'NUMERIC(' || c.numeric_precision || ',' || c.numeric_scale || ')'
              WHEN c.data_type = 'integer' THEN 'INTEGER'
              WHEN c.data_type = 'bigint' THEN 'BIGINT'
              WHEN c.data_type = 'smallint' THEN 'SMALLINT'
              WHEN c.data_type = 'boolean' THEN 'BOOLEAN'
              WHEN c.data_type = 'text' THEN 'TEXT'
              WHEN c.data_type = 'timestamp without time zone' THEN 'TIMESTAMP'
              WHEN c.data_type = 'timestamp with time zone' THEN 'TIMESTAMPTZ'
              WHEN c.data_type = 'date' THEN 'DATE'
              WHEN c.data_type = 'time without time zone' THEN 'TIME'
              WHEN c.data_type = 'uuid' THEN 'UUID'
              WHEN c.data_type = 'json' THEN 'JSON'
              WHEN c.data_type = 'jsonb' THEN 'JSONB'
              ELSE UPPER(c.data_type)
            END ||
            CASE WHEN c.is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
            CASE WHEN c.column_default IS NOT NULL THEN ' DEFAULT ' || c.column_default ELSE '' END,
            E',\n' ORDER BY c.ordinal_position
          ) as columns_def
        FROM information_schema.tables t
        JOIN information_schema.columns c ON t.table_name = c.table_name
        WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        GROUP BY t.table_name
      ),
      primary_keys AS (
        SELECT 
          tc.table_name,
          string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) as pk_columns
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
        WHERE tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
        GROUP BY tc.table_name
      )
      SELECT 
        'CREATE TABLE ' || td.table_name || ' (' || E'\n' ||
        td.columns_def ||
        CASE 
          WHEN pk.pk_columns IS NOT NULL THEN E',\n  PRIMARY KEY (' || pk.pk_columns || ')'
          ELSE ''
        END ||
        E'\n);' || E'\n' as ddl_statement
      FROM table_definitions td
      LEFT JOIN primary_keys pk ON td.table_name = pk.table_name
      ORDER BY td.table_name;

  analyze-schema-and-query:
    kind: postgres-sql
    source: my-pg-source
    description: Analyze database schema from DDL and execute a custom query based on the schema understanding.
    parameters:
      - name: analysis_query
        type: string
        description: The SQL query to execute after understanding the schema structure.
    statement: "{analysis_query}"

 

toolsets:
  default:
    - search-hotels-by-name
    - generate-all-tables-ddl
    - analyze-schema-and-query
    - list-tables
   
    

# toolsets:
#     hotel_toolset:
#         -  search-hotels-by-name
      

# all_tools = client.load_toolset()